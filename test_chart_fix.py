"""
Test script to verify the chart downloader async fix
"""
import asyncio
import aiohttp
import sys
import pandas as pd

# Add src directory to Python path
sys.path.append('src')

from fidelity.chart_analysis.chart_downloader import filter_stocks

async def test_filter_stocks():
    """Test the filter_stocks function with a sample DataFrame."""
    print("Testing filter_stocks with AAPL...")
    
    # Create a simple test DataFrame
    df = pd.DataFrame({
        'Symbol': ['AAPL']
    })
    
    try:
        async with aiohttp.ClientSession() as session:
            filtered_df, excluded_tickers = await filter_stocks(session, df)
            
        print("Success! filter_stocks completed without errors")
        print(f"Filtered DataFrame shape: {filtered_df.shape}")
        print(f"Excluded tickers: {excluded_tickers}")
        return True
        
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_filter_stocks())
